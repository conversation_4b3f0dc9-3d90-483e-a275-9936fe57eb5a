package com.decurret_dcp.dcjpy.bcmonitoring.adaptor.infrastructure.ethereum;

import com.decurret_dcp.dcjpy.bcmonitoring.adaptor.infrastructure.s3.AbiParser;
import com.decurret_dcp.dcjpy.bcmonitoring.config.BcmonitoringConfigurationProperties;
import com.decurret_dcp.dcjpy.bcmonitoring.config.Web3jConfig;
import com.decurret_dcp.dcjpy.bcmonitoring.domain.model.BlockHeight;
import com.decurret_dcp.dcjpy.bcmonitoring.domain.model.Event;
import com.decurret_dcp.dcjpy.bcmonitoring.domain.model.Transaction;
import com.decurret_dcp.dcjpy.bcmonitoring.logging.LoggingService;
import com.fasterxml.jackson.databind.ObjectMapper;
import io.reactivex.disposables.Disposable;
import java.io.IOException;
import java.math.BigInteger;
import java.time.Instant;
import java.util.*;
import java.util.concurrent.BlockingQueue;
import java.util.concurrent.ExecutionException;
import java.util.concurrent.LinkedBlockingQueue;
import org.springframework.stereotype.Component;
import org.web3j.abi.EventValues;
import org.web3j.abi.datatypes.Type;
import org.web3j.protocol.Web3j;
import org.web3j.protocol.core.DefaultBlockParameter;
import org.web3j.protocol.core.DefaultBlockParameterNumber;
import org.web3j.protocol.core.methods.request.EthFilter;
import org.web3j.protocol.core.methods.response.*;
import org.web3j.tx.Contract;

@Component
public class EthEventLogDao {
  private final LoggingService logger;
  private final BcmonitoringConfigurationProperties properties;
  private final Web3jConfig web3jConfig;
  private final AbiParser abiParser;
  private final ObjectMapper objectMapper;
  private Disposable subscription;

  /**
   * Constructor for EthEventLogDao.
   *
   * @param log The logging service.
   * @param properties The configuration properties.
   * @param web3jConfig The Web3j configuration.
   * @param abiParser The ABI parser.
   * @param objectMapper The object mapper.
   */
  public EthEventLogDao(
      LoggingService log,
      BcmonitoringConfigurationProperties properties,
      Web3jConfig web3jConfig,
      AbiParser abiParser,
      ObjectMapper objectMapper) {
    this.logger = log;
    this.properties = properties;
    this.web3jConfig = web3jConfig;
    this.abiParser = abiParser;
    this.objectMapper = objectMapper;
  }

  /**
   * Subscribes to all blocks and processes transactions.
   *
   * @return A BlockingQueue of Transaction objects.
   */
  public BlockingQueue<Transaction> subscribeAll() {
    BlockingQueue<Transaction> transactions = new LinkedBlockingQueue<>(Integer.MAX_VALUE);

    // Check if the difference is valid
    int allowableDiff;
    try {
      allowableDiff =
          Integer.parseInt(properties.getSubscription().getAllowableBlockTimestampDiffSec());
    } catch (NumberFormatException e) {
      logger.error("Failed to parse allowable timestamp difference", e);
      return null;
    }

    try {
      // Create a new Web3j instance for this subscription
      Web3j web3j = web3jConfig.getWeb3j();
      // Subscribe to new blocks
      this.subscription =
          web3j
              .newHeadsNotifications()
              .subscribe(
                  newHeadsNotification -> {
                    try {
                      web3j
                          .ethGetBlockByNumber(
                              () -> newHeadsNotification.getParams().getResult().getNumber(), true)
                          .sendAsync()
                          .thenApply(EthBlock::getBlock)
                          .thenAccept(
                              block -> {
                                BigInteger blockNumber = block.getNumber();

                                // Check delay in block processing
                                if (isDelayed(block, allowableDiff)) {
                                  logger.warn(
                                      "Block {} is delayed by more than {} seconds",
                                      blockNumber,
                                      allowableDiff);
                                }

                                // Process block transactions and events
                                List<Event> events = null;
                                try {
                                  events = convBlock2EventEntities(block);
                                } catch (Exception e) {
                                  throw new RuntimeException(e);
                                }

                                // Always create transaction, even if events list is empty
                                if (events != null) {
                                  if (!events.isEmpty()) {
                                    logger.info("detect block includes events");
                                  }

                                  BlockHeight blockHeight =
                                      BlockHeight.builder()
                                          .blockNumber(blockNumber.longValue())
                                          .build();
                                  Transaction transaction =
                                      Transaction.builder()
                                          .events(events)
                                          .blockHeight(blockHeight)
                                          .build();

                                  try {
                                    transactions.put(transaction);
                                  } catch (InterruptedException e) {
                                    throw new RuntimeException(e);
                                  }
                                }
                              })
                          .exceptionally(
                              throwable -> {
                                logger.error("Error in CompletableFuture chain", throwable);
                                // Put error transaction to signal error condition
                                try {
                                  transactions.put(
                                      Transaction.builder()
                                          .blockHeight(
                                              BlockHeight.builder().blockNumber(-1).build())
                                          .build());
                                } catch (InterruptedException e) {
                                  logger.error("Failed to put error transaction", e);
                                  Thread.currentThread().interrupt();
                                }
                                return null;
                              });
                    } catch (Exception e) {
                      logger.error("Error processing block", e);
                    }
                  },
                  error -> {
                    logger.error("Subscription error", error);
                    unsubscribe();
                    web3jConfig.shutdownWeb3j();
                    transactions.put(
                        Transaction.builder()
                            .blockHeight(BlockHeight.builder().blockNumber(-1).build())
                            .build());
                  },
                  () -> logger.info("Subscription completed"));
      return transactions;
    } catch (Exception e) {
      logger.error("Failed to create Web3j subscription", e);
      throw e;
    }
  }

  /**
   * Checks if the block is delayed based on the allowable difference in seconds.
   *
   * @param block The block to check.
   * @param allowableDiffSeconds The allowable difference in seconds.
   * @return true if the block is delayed, false otherwise.
   */
  private boolean isDelayed(EthBlock.Block block, int allowableDiffSeconds) {
    long blockTimestamp = block.getTimestamp().longValue();
    long currentTime = Instant.now().getEpochSecond();
    long diff = currentTime - blockTimestamp;

    return diff > allowableDiffSeconds;
  }

  /**
   * Converts a block to a collection of event entities.
   *
   * @param block Ethereum block
   * @return Collection of events found in the block
   * @throws IOException If there is an error communicating with the Ethereum node
   * @throws ExecutionException If there is an error executing the transaction
   * @throws InterruptedException If the operation is interrupted
   */
  public List<Event> convBlock2EventEntities(EthBlock.Block block) throws Exception {
    List<Event> events = new ArrayList<>();

    try {
      // Create a new Web3j instance for this operation
      Web3j web3j = web3jConfig.getWeb3j();

      for (EthBlock.TransactionResult txResult : block.getTransactions()) {
        // Return error if transaction is null
        if (txResult.get() == null) {
          throw new RuntimeException("Transaction is null");
        }

        try {
          EthGetTransactionReceipt receiptResponse =
              web3j.ethGetTransactionReceipt(txResult.get().toString()).send();

          TransactionReceipt receipt = receiptResponse.getTransactionReceipt().orElse(null);
          if (receipt == null) {
            throw new RuntimeException("Transaction receipt is null");
          }

          for (Log log : receipt.getLogs()) {
            try {
              logger.info("Event found tx_hash={}", log.getTransactionHash());
              Event event =
                  convertEthLogToEventEntity(log)
                      .withBlockTimestamp(block.getTimestamp().longValue());
              logger.info("Event parsed tx_hash={}, name={}", event.transactionHash, event.name);

              if (event.transactionHash != null && !event.transactionHash.isEmpty()) {
                events.add(event);
              }
            } catch (Exception e) {
              logger.error("Error processing log for transaction {}", log.getTransactionHash());
              throw e;
            }
          }
        } catch (Exception e) {
          logger.error("Error processing transaction", e.getMessage());
          throw e;
        }
      }
    } catch (Exception e) {
      logger.error("Error converting block to events: {}", e.getMessage());
      throw e;
    }

    return events;
  }

  /**
   * Converts an Ethereum log to an Event entity.
   *
   * @param ethLog The Ethereum log to convert
   * @return Converted Event entity
   * @throws Exception If conversion fails
   */
  public Event convertEthLogToEventEntity(Log ethLog) throws Exception {
    try {
      // Get ABI event definition for the log
      org.web3j.abi.datatypes.Event abiEvent = abiParser.getABIEventByLog(ethLog);
      if (abiEvent == null) {
        logger.info("Event definition not found in ABI");
        throw new Exception("Event definition not found in ABI");
      }

      // Get contract ABI event to access parameter names
      var contractAbiEvent = abiParser.getContractAbiEventByLog(ethLog);

      // Extract event parameters using enhanced tuple-aware extraction
      EventValues eventValues = extractEventParametersWithTupleSupport(abiEvent, ethLog, contractAbiEvent);
      if (eventValues == null) {
        logger.info("No event values found for log: {}", ethLog);
        throw new Exception("No event values found for log");
      }

      // Process indexed parameters with actual parameter names and tuple support
      Map<String, Object> indexedValues = new HashMap<>();
      List<Type> indexedParameters = eventValues.getIndexedValues();

      int indexedIndex = 0;
      for (var input : contractAbiEvent.getInputs()) {
        if (input.isIndexed() && indexedIndex < indexedParameters.size()) {
          String name = input.getName();
          Object value = indexedParameters.get(indexedIndex).getValue();

          // Handle tuple types with component extraction
          if (input.isTuple()) {
            Object processedValue = processTupleValue(value, input, name);
            indexedValues.put(name, processedValue);
          } else {
            // Convert byte arrays to Lists to ensure proper JSON array serialization
            if (value instanceof byte[]) {
              byte[] byteArray = (byte[]) value;
              List<Integer> byteList = new ArrayList<>();
              for (byte b : byteArray) {
                byteList.add((int) b);
              }
              indexedValues.put(name, byteList);
            } else {
              indexedValues.put(name, value);
            }
          }
          indexedIndex++;
        }
      }
      String indexedJson = objectMapper.writeValueAsString(indexedValues);

      // Process non-indexed parameters with actual parameter names
      Map<String, Object> nonIndexedValues = new HashMap<>();
      List<Type> nonIndexedParameters = eventValues.getNonIndexedValues();

      int nonIndexedIndex = 0;
      for (var input : contractAbiEvent.getInputs()) {
        if (!input.isIndexed() && nonIndexedIndex < nonIndexedParameters.size()) {
          String name = input.getName();
          Object value = nonIndexedParameters.get(nonIndexedIndex).getValue();
          // Convert byte arrays to Lists to ensure proper JSON array serialization
          if (value instanceof byte[]) {
            byte[] byteArray = (byte[]) value;
            List<Integer> byteList = new ArrayList<>();
            for (byte b : byteArray) {
              byteList.add((int) b);
            }
            nonIndexedValues.put(name, byteList);
          } else {
            nonIndexedValues.put(name, value);
          }
          nonIndexedIndex++;
        }
      }
      String nonIndexedJson = objectMapper.writeValueAsString(nonIndexedValues);

      // Serialize log to JSON
      String logJson = objectMapper.writeValueAsString(ethLog);

      // Create and return new Event entity
      return Event.builder()
          .name(abiEvent.getName())
          .transactionHash(ethLog.getTransactionHash())
          .logIndex((int) ethLog.getLogIndex().longValue())
          .indexedValues(indexedJson)
          .nonIndexedValues(nonIndexedJson)
          .log(logJson)
          .build();
    } catch (Exception e) {
      logger.error("Error converting log to event entity", e);
      return null;
    }
  }

  /**
   * Enhanced event parameter extraction with tuple support.
   * This method handles tuple types by extracting individual components instead of opaque objects.
   *
   * @param abiEvent The Web3j event definition
   * @param ethLog The Ethereum log
   * @param contractAbiEvent The enhanced contract ABI event with tuple component information
   * @return EventValues with properly extracted tuple components
   */
  private EventValues extractEventParametersWithTupleSupport(
      org.web3j.abi.datatypes.Event abiEvent,
      Log ethLog,
      AbiParser.ContractAbiEvent contractAbiEvent) {

    try {
      // First, try the standard Web3j extraction
      EventValues standardEventValues = Contract.staticExtractEventParameters(abiEvent, ethLog);

      // If standard extraction fails or we don't have enhanced contract info, return standard result
      if (standardEventValues == null || contractAbiEvent == null) {
        return standardEventValues;
      }

      // Check if any parameters are tuple types that need enhanced processing
      boolean hasTupleTypes = contractAbiEvent.getInputs().stream()
          .anyMatch(input -> input.isTuple());

      // If no tuple types, return standard result
      if (!hasTupleTypes) {
        return standardEventValues;
      }

      logger.debug("Processing event with tuple types: " + abiEvent.getName());

      // For now, return the standard extraction as a fallback
      // TODO: Implement full tuple component extraction when needed
      // This would involve manually parsing the log data and topics
      // to extract individual tuple components based on contractAbiEvent structure

      logger.info("Tuple type detected in event {}, using standard extraction as fallback",
                  abiEvent.getName());
      return standardEventValues;

    } catch (Exception e) {
      logger.error("Error in tuple-aware event parameter extraction for event: {}",
                   abiEvent.getName(), e);

      // Fallback to standard extraction
      try {
        return Contract.staticExtractEventParameters(abiEvent, ethLog);
      } catch (Exception fallbackException) {
        logger.error("Fallback extraction also failed", fallbackException);
        return null;
      }
    }
  }

  /**
   * Process tuple values to extract individual components.
   * This method attempts to extract meaningful information from tuple objects.
   *
   * @param value The tuple value object from Web3j
   * @param input The ABI input definition with component information
   * @param parameterName The parameter name for logging
   * @return Processed tuple value (for now, returns original value with logging)
   */
  private Object processTupleValue(Object value, AbiParser.AbiEventInput input, String parameterName) {
    try {
      logger.debug("Processing tuple parameter '" + parameterName + "' of type '" + input.getType() +
                   "' with " + (input.getComponents() != null ? input.getComponents().size() : 0) + " components");

      // Log component information for debugging
      if (input.getComponents() != null) {
        for (var component : input.getComponents()) {
          logger.debug("  Component: " + component.getName() + " (type: " + component.getType() +
                       ", indexed: " + component.isIndexed() + ")");
        }
      }

      // For now, return the original value
      // TODO: Implement full tuple component extraction
      // This would involve:
      // 1. Checking if value is a Web3j Struct type
      // 2. Extracting individual component values
      // 3. Creating a structured object with component names and values
      // 4. Handling nested tuples recursively

      logger.info("Tuple parameter '{}' processed (using original value as fallback)", parameterName);
      return value;

    } catch (Exception e) {
      logger.error("Error processing tuple parameter '{}': {}", parameterName, e.getMessage());
      return value; // Return original value as fallback
    }
  }

  /**
   * Retrieves the block timestamp for a given block number.
   *
   * @param blockNumber The block number to retrieve the timestamp for.
   * @return The block timestamp in seconds since epoch.
   * @throws IOException If there is an error communicating with the Ethereum node
   */
  private long getBlockTimestamp(BigInteger blockNumber) throws IOException {
    // Create a new Web3j instance for this operation
    Web3j web3j = web3jConfig.getWeb3j();

    try {
      return web3j
          .ethGetBlockByNumber(DefaultBlockParameter.valueOf(blockNumber), false)
          .send()
          .getBlock()
          .getTimestamp()
          .longValue();
    } finally {
      // Shutdown the Web3j instance to free resources
      web3j.shutdown();
    }
  }

  /**
   * Get filtered logs from a specific block height
   *
   * @param blockHeight Block number to start from
   * @return Queue of transactions containing events
   */
  public List<Transaction> getPendingTransactions(long blockHeight) {
    return getPendingTransactions(blockHeight, false);
  }

  /**
   * Get filtered logs from a specific block height with an option to force an error in the outer
   * catch block This method is primarily used for testing the error handling in the outer catch
   * block
   *
   * @param blockHeight Block number to start from
   * @param forceOuterError Whether to force an error in the outer catch block (for testing)
   * @return Queue of transactions containing events
   */
  public List<Transaction> getPendingTransactions(long blockHeight, boolean forceOuterError) {
    try {
      // Create a new Web3j instance for this operation
      Web3j web3j = web3jConfig.getWeb3j();

      // Create filter to get logs from the specified block height
      EthFilter filter =
          new EthFilter(
              DefaultBlockParameter.valueOf(BigInteger.valueOf(blockHeight)),
              DefaultBlockParameter.valueOf("latest"),
              Collections.emptyList());

      // Get logs synchronously
      List<EthLog.LogResult> filterLogs = web3j.ethGetLogs(filter).send().getLogs();

      logger.info(
          "Retrieved {} logs from block height {} to latest", filterLogs.size(), blockHeight);

      // Collect block numbers
      List<BigInteger> blockNumbers =
          filterLogs.stream().map(result -> (Log) result.get()).map(Log::getBlockNumber).toList();

      // Fetch timestamps per block
      Map<BigInteger, BigInteger> blockTimestamps = new HashMap<>();
      for (BigInteger blockNumber : blockNumbers) {
        EthBlock block =
            web3j.ethGetBlockByNumber(new DefaultBlockParameterNumber(blockNumber), false).send();
        blockTimestamps.put(blockNumber, block.getBlock().getTimestamp());
      }

      if (forceOuterError) {
        throw new RuntimeException("Forced error in outer catch block for testing");
      }

      return filterLogs.stream()
          .map(
              logResult -> {
                try {
                  Log ethLog = (Log) logResult.get();
                  logger.info("Event found tx_hash={}", ethLog.getTransactionHash());

                  Event event =
                      convertEthLogToEventEntity(ethLog)
                          .withBlockTimestamp(
                              blockTimestamps.get(ethLog.getBlockNumber()).longValue());
                  logger.info(
                      "Event parsed tx_hash={}, name={}", event.transactionHash, event.name);

                  BlockHeight height =
                      BlockHeight.builder()
                          .blockNumber(ethLog.getBlockNumber().longValue())
                          .build();

                  return Transaction.builder()
                      .events(Collections.singletonList(event))
                      .blockHeight(height)
                      .build();
                } catch (Exception e) {
                  logger.error("Error processing individual log", e);
                  return null;
                }
              })
          .filter(Objects::nonNull)
          .toList();

    } catch (Exception e) {
      logger.error("Error getting filtered logs", e);
      throw new RuntimeException("Error getting filtered logs", e);
    }
  }

  public void unsubscribe() {
    if (subscription != null) {
      subscription.dispose();
    }
  }
}
