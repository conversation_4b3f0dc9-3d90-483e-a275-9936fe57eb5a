package com.decurret_dcp.dcjpy.bcmonitoring.adaptor.infrastructure.s3;

import com.decurret_dcp.dcjpy.bcmonitoring.config.BcmonitoringConfigurationProperties;
import org.junit.jupiter.api.BeforeEach;
import org.junit.jupiter.api.Test;
import org.mockito.Mock;
import org.mockito.MockitoAnnotations;

import java.io.IOException;

import static org.junit.jupiter.api.Assertions.*;

public class AbiParserTupleTest {

    @Mock
    private BcmonitoringConfigurationProperties properties;

    private AbiParser abiParser;

    @BeforeEach
    void setUp() {
        MockitoAnnotations.openMocks(this);
        abiParser = new AbiParser(properties);
    }

    @Test
    void testParseAbiWithTupleTypes() throws IOException {
        // Test ABI JSON with tuple types (similar to the TupleTypes.json example)
        String abiJson = """
            [
              {
                "anonymous": false,
                "inputs": [
                  {
                    "indexed": true,
                    "internalType": "uint256",
                    "name": "tupleDataId",
                    "type": "uint256"
                  },
                  {
                    "indexed": false,
                    "internalType": "struct TupleData",
                    "name": "tupleData",
                    "type": "tuple",
                    "components": [
                      {
                        "internalType": "bytes32",
                        "name": "someByte",
                        "type": "bytes32"
                      },
                      {
                        "internalType": "uint256",
                        "name": "someUint",
                        "type": "uint256"
                      },
                      {
                        "internalType": "bool",
                        "name": "someBool",
                        "type": "bool"
                      }
                    ]
                  }
                ],
                "name": "TupleEvent",
                "type": "event"
              }
            ]
            """;

        var result = abiParser.parseAbi(abiJson);

        // Verify that the parsing completed without errors
        assertNotNull(result);
        assertEquals(1, result.size());

        // Verify that the event was parsed
        var eventEntry = result.entrySet().iterator().next();
        assertNotNull(eventEntry.getKey()); // Event signature
        assertNotNull(eventEntry.getValue()); // ContractAbiEvent

        var contractAbiEvent = eventEntry.getValue();
        assertEquals("TupleEvent", contractAbiEvent.getEvent().getName());
        assertEquals(2, contractAbiEvent.getInputs().size());

        // Verify the inputs
        var inputs = contractAbiEvent.getInputs();
        assertEquals("tupleDataId", inputs.get(0).getName());
        assertEquals("uint256", inputs.get(0).getType());
        assertTrue(inputs.get(0).isIndexed());
        assertFalse(inputs.get(0).isTuple());

        assertEquals("tupleData", inputs.get(1).getName());
        assertEquals("tuple", inputs.get(1).getType());
        assertFalse(inputs.get(1).isIndexed());
        assertTrue(inputs.get(1).isTuple());

        // Verify tuple components are extracted
        var tupleComponents = inputs.get(1).getComponents();
        assertNotNull(tupleComponents);
        assertEquals(3, tupleComponents.size());

        // Verify individual tuple components
        assertEquals("someByte", tupleComponents.get(0).getName());
        assertEquals("bytes32", tupleComponents.get(0).getType());
        assertFalse(tupleComponents.get(0).isIndexed());
        assertFalse(tupleComponents.get(0).isTuple());

        assertEquals("someUint", tupleComponents.get(1).getName());
        assertEquals("uint256", tupleComponents.get(1).getType());
        assertFalse(tupleComponents.get(1).isIndexed());
        assertFalse(tupleComponents.get(1).isTuple());

        assertEquals("someBool", tupleComponents.get(2).getName());
        assertEquals("bool", tupleComponents.get(2).getType());
        assertFalse(tupleComponents.get(2).isIndexed());
        assertFalse(tupleComponents.get(2).isTuple());
    }

    @Test
    void testParseAbiWithTupleArrayTypes() throws IOException {
        // Test ABI JSON with tuple array types
        String abiJson = """
            [
              {
                "anonymous": false,
                "inputs": [
                  {
                    "indexed": false,
                    "internalType": "struct TupleData[]",
                    "name": "tupleDataList",
                    "type": "tuple[]",
                    "components": [
                      {
                        "internalType": "bytes32",
                        "name": "someByte",
                        "type": "bytes32"
                      },
                      {
                        "internalType": "uint256",
                        "name": "someUint",
                        "type": "uint256"
                      }
                    ]
                  }
                ],
                "name": "TupleArrayEvent",
                "type": "event"
              }
            ]
            """;

        var result = abiParser.parseAbi(abiJson);

        // Verify that the parsing completed without errors
        assertNotNull(result);
        assertEquals(1, result.size());

        // Verify that the event was parsed
        var eventEntry = result.entrySet().iterator().next();
        assertNotNull(eventEntry.getKey()); // Event signature
        assertNotNull(eventEntry.getValue()); // ContractAbiEvent

        var contractAbiEvent = eventEntry.getValue();
        assertEquals("TupleArrayEvent", contractAbiEvent.getEvent().getName());
        assertEquals(1, contractAbiEvent.getInputs().size());

        // Verify the input
        var input = contractAbiEvent.getInputs().get(0);
        assertEquals("tupleDataList", input.getName());
        assertEquals("tuple[]", input.getType());
        assertFalse(input.isIndexed());
        assertTrue(input.isTuple());

        // Verify tuple array components are extracted
        var tupleComponents = input.getComponents();
        assertNotNull(tupleComponents);
        assertEquals(2, tupleComponents.size());

        // Verify individual tuple components
        assertEquals("someByte", tupleComponents.get(0).getName());
        assertEquals("bytes32", tupleComponents.get(0).getType());
        assertFalse(tupleComponents.get(0).isIndexed());
        assertFalse(tupleComponents.get(0).isTuple());

        assertEquals("someUint", tupleComponents.get(1).getName());
        assertEquals("uint256", tupleComponents.get(1).getType());
        assertFalse(tupleComponents.get(1).isIndexed());
        assertFalse(tupleComponents.get(1).isTuple());
    }

    @Test
    void testParseAbiWithMixedTypes() throws IOException {
        // Test ABI JSON with both regular and tuple types
        String abiJson = """
            [
              {
                "anonymous": false,
                "inputs": [
                  {
                    "indexed": true,
                    "internalType": "address",
                    "name": "user",
                    "type": "address"
                  },
                  {
                    "indexed": false,
                    "internalType": "uint256",
                    "name": "amount",
                    "type": "uint256"
                  },
                  {
                    "indexed": false,
                    "internalType": "struct UserData",
                    "name": "userData",
                    "type": "tuple",
                    "components": [
                      {
                        "internalType": "string",
                        "name": "name",
                        "type": "string"
                      },
                      {
                        "internalType": "bool",
                        "name": "active",
                        "type": "bool"
                      }
                    ]
                  }
                ],
                "name": "MixedEvent",
                "type": "event"
              }
            ]
            """;

        var result = abiParser.parseAbi(abiJson);

        // Verify that the parsing completed without errors
        assertNotNull(result);
        assertEquals(1, result.size());

        // Verify that the event was parsed
        var eventEntry = result.entrySet().iterator().next();
        var contractAbiEvent = eventEntry.getValue();
        assertEquals("MixedEvent", contractAbiEvent.getEvent().getName());
        assertEquals(3, contractAbiEvent.getInputs().size());

        // Verify the inputs
        var inputs = contractAbiEvent.getInputs();
        assertEquals("user", inputs.get(0).getName());
        assertEquals("address", inputs.get(0).getType());
        assertTrue(inputs.get(0).isIndexed());
        assertFalse(inputs.get(0).isTuple());

        assertEquals("amount", inputs.get(1).getName());
        assertEquals("uint256", inputs.get(1).getType());
        assertFalse(inputs.get(1).isIndexed());
        assertFalse(inputs.get(1).isTuple());

        assertEquals("userData", inputs.get(2).getName());
        assertEquals("tuple", inputs.get(2).getType());
        assertFalse(inputs.get(2).isIndexed());
        assertTrue(inputs.get(2).isTuple());

        // Verify tuple components are extracted
        var tupleComponents = inputs.get(2).getComponents();
        assertNotNull(tupleComponents);
        assertEquals(2, tupleComponents.size());

        // Verify individual tuple components
        assertEquals("name", tupleComponents.get(0).getName());
        assertEquals("string", tupleComponents.get(0).getType());
        assertFalse(tupleComponents.get(0).isIndexed());
        assertFalse(tupleComponents.get(0).isTuple());

        assertEquals("active", tupleComponents.get(1).getName());
        assertEquals("bool", tupleComponents.get(1).getType());
        assertFalse(tupleComponents.get(1).isIndexed());
        assertFalse(tupleComponents.get(1).isTuple());
    }

    @Test
    void testParseAbiWithNestedTuples() throws IOException {
        // Test ABI JSON with nested tuple types (tuple within tuple)
        String abiJson = """
            [
              {
                "anonymous": false,
                "inputs": [
                  {
                    "indexed": false,
                    "internalType": "struct NestedData",
                    "name": "nestedData",
                    "type": "tuple",
                    "components": [
                      {
                        "internalType": "uint256",
                        "name": "id",
                        "type": "uint256"
                      },
                      {
                        "internalType": "struct InnerData",
                        "name": "innerData",
                        "type": "tuple",
                        "components": [
                          {
                            "internalType": "string",
                            "name": "description",
                            "type": "string"
                          },
                          {
                            "internalType": "bool",
                            "name": "enabled",
                            "type": "bool"
                          }
                        ]
                      }
                    ]
                  }
                ],
                "name": "NestedTupleEvent",
                "type": "event"
              }
            ]
            """;

        var result = abiParser.parseAbi(abiJson);

        // Verify that the parsing completed without errors
        assertNotNull(result);
        assertEquals(1, result.size());

        // Verify that the event was parsed
        var eventEntry = result.entrySet().iterator().next();
        var contractAbiEvent = eventEntry.getValue();
        assertEquals("NestedTupleEvent", contractAbiEvent.getEvent().getName());
        assertEquals(1, contractAbiEvent.getInputs().size());

        // Verify the outer tuple
        var outerTuple = contractAbiEvent.getInputs().get(0);
        assertEquals("nestedData", outerTuple.getName());
        assertEquals("tuple", outerTuple.getType());
        assertFalse(outerTuple.isIndexed());
        assertTrue(outerTuple.isTuple());

        // Verify outer tuple components
        var outerComponents = outerTuple.getComponents();
        assertNotNull(outerComponents);
        assertEquals(2, outerComponents.size());

        // Verify first component (simple type)
        assertEquals("id", outerComponents.get(0).getName());
        assertEquals("uint256", outerComponents.get(0).getType());
        assertFalse(outerComponents.get(0).isIndexed());
        assertFalse(outerComponents.get(0).isTuple());

        // Verify second component (nested tuple)
        var innerTuple = outerComponents.get(1);
        assertEquals("innerData", innerTuple.getName());
        assertEquals("tuple", innerTuple.getType());
        assertFalse(innerTuple.isIndexed());
        assertTrue(innerTuple.isTuple());

        // Verify inner tuple components
        var innerComponents = innerTuple.getComponents();
        assertNotNull(innerComponents);
        assertEquals(2, innerComponents.size());

        assertEquals("description", innerComponents.get(0).getName());
        assertEquals("string", innerComponents.get(0).getType());
        assertFalse(innerComponents.get(0).isIndexed());
        assertFalse(innerComponents.get(0).isTuple());

        assertEquals("enabled", innerComponents.get(1).getName());
        assertEquals("bool", innerComponents.get(1).getType());
        assertFalse(innerComponents.get(1).isIndexed());
        assertFalse(innerComponents.get(1).isTuple());
    }
}
