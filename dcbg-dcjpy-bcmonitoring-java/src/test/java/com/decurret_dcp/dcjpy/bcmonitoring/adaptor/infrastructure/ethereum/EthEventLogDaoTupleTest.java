package com.decurret_dcp.dcjpy.bcmonitoring.adaptor.infrastructure.ethereum;

import com.decurret_dcp.dcjpy.bcmonitoring.adaptor.infrastructure.s3.AbiParser;
import com.decurret_dcp.dcjpy.bcmonitoring.config.BcmonitoringConfigurationProperties;
import com.decurret_dcp.dcjpy.bcmonitoring.config.Web3jConfig;
import com.decurret_dcp.dcjpy.bcmonitoring.logging.LoggingService;
import com.fasterxml.jackson.databind.ObjectMapper;
import org.junit.jupiter.api.BeforeEach;
import org.junit.jupiter.api.Test;
import org.mockito.Mock;
import org.mockito.MockitoAnnotations;
import org.web3j.abi.EventEncoder;
import org.web3j.abi.TypeReference;
import org.web3j.abi.datatypes.Event;
import org.web3j.abi.datatypes.Type;
import org.web3j.abi.datatypes.DynamicBytes;
import org.web3j.abi.datatypes.generated.Uint256;
import org.web3j.abi.datatypes.generated.Bytes32;
import org.web3j.abi.datatypes.Bool;
import org.web3j.protocol.core.methods.response.Log;

import java.lang.reflect.Method;
import java.util.Arrays;
import java.util.List;

import static org.junit.jupiter.api.Assertions.*;
import static org.mockito.ArgumentMatchers.any;
import static org.mockito.Mockito.when;

public class EthEventLogDaoTupleTest {

    @Mock
    private LoggingService mockLogger;

    @Mock
    private BcmonitoringConfigurationProperties mockProperties;

    @Mock
    private Web3jConfig mockWeb3jConfig;

    @Mock
    private AbiParser mockAbiParser;

    @Mock
    private ObjectMapper mockObjectMapper;

    private EthEventLogDao ethEventLogDao;

    @BeforeEach
    void setUp() {
        MockitoAnnotations.openMocks(this);
        ethEventLogDao = new EthEventLogDao(mockLogger, mockProperties, mockWeb3jConfig, mockAbiParser, mockObjectMapper);
    }

    @Test
    void testTupleDetectionLogic() {
        // Test that our tuple detection logic works correctly in ContractAbiEvent
        var tupleComponents = Arrays.asList(
            new AbiParser.AbiEventInput("field1", "uint256", false),
            new AbiParser.AbiEventInput("field2", "string", false)
        );

        var eventInputs = Arrays.asList(
            new AbiParser.AbiEventInput("regularParam", "uint256", true),
            new AbiParser.AbiEventInput("tupleParam", "tuple", false, tupleComponents),
            new AbiParser.AbiEventInput("tupleArrayParam", "tuple[]", false, tupleComponents)
        );

        // Create a simple Web3j Event
        List<TypeReference<?>> parameters = Arrays.asList(
            new TypeReference<Uint256>(true) {},
            new TypeReference<DynamicBytes>(false) {},
            new TypeReference<DynamicBytes>(false) {}
        );
        Event abiEvent = new Event("TestEvent", parameters);

        var contractAbiEvent = new AbiParser.ContractAbiEvent(abiEvent, eventInputs);

        // Test the hasTupleTypes detection logic that would be used in extractEventParametersWithTupleSupport
        boolean hasTupleTypes = contractAbiEvent.getInputs().stream()
            .anyMatch(input -> input.isTuple());

        assertTrue(hasTupleTypes, "Should detect tuple types in event inputs");

        // Verify individual tuple detection
        assertFalse(eventInputs.get(0).isTuple());
        assertTrue(eventInputs.get(1).isTuple());
        assertTrue(eventInputs.get(2).isTuple());

        // Verify component access
        assertNull(eventInputs.get(0).getComponents());
        assertNotNull(eventInputs.get(1).getComponents());
        assertEquals(2, eventInputs.get(1).getComponents().size());
        assertEquals("field1", eventInputs.get(1).getComponents().get(0).getName());
        assertEquals("uint256", eventInputs.get(1).getComponents().get(0).getType());

        System.out.println("✅ Tuple detection logic works correctly!");
        System.out.println("Event has tuple types: " + hasTupleTypes);
        System.out.println("Tuple parameter components: " + eventInputs.get(1).getComponents().size());
    }

    @Test
    void testTupleDetectionInContractAbiEvent() {
        // Test that our tuple detection logic works correctly
        var tupleComponents = Arrays.asList(
            new AbiParser.AbiEventInput("field1", "uint256", false),
            new AbiParser.AbiEventInput("field2", "string", false)
        );
        
        var eventInputs = Arrays.asList(
            new AbiParser.AbiEventInput("regularParam", "uint256", true),
            new AbiParser.AbiEventInput("tupleParam", "tuple", false, tupleComponents),
            new AbiParser.AbiEventInput("tupleArrayParam", "tuple[]", false, tupleComponents)
        );
        
        // Verify tuple detection
        assertFalse(eventInputs.get(0).isTuple());
        assertTrue(eventInputs.get(1).isTuple());
        assertTrue(eventInputs.get(2).isTuple());
        
        // Verify component access
        assertNull(eventInputs.get(0).getComponents());
        assertNotNull(eventInputs.get(1).getComponents());
        assertEquals(2, eventInputs.get(1).getComponents().size());
        assertEquals("field1", eventInputs.get(1).getComponents().get(0).getName());
        assertEquals("uint256", eventInputs.get(1).getComponents().get(0).getType());
    }

    @Test
    void testProcessTupleValueLogging() {
        // Test that processTupleValue logs the correct information
        var tupleComponents = Arrays.asList(
            new AbiParser.AbiEventInput("field1", "uint256", false),
            new AbiParser.AbiEventInput("field2", "string", false)
        );
        
        var tupleInput = new AbiParser.AbiEventInput("testTuple", "tuple", false, tupleComponents);
        
        // Create a mock tuple value (this would normally come from Web3j)
        Object mockTupleValue = "mock_tuple_data";
        
        // This test mainly verifies that the method doesn't crash and logs appropriately
        // The actual tuple processing is still TODO in the implementation
        assertDoesNotThrow(() -> {
            // We can't directly call processTupleValue as it's private, 
            // but we can verify the structure is correct
            assertTrue(tupleInput.isTuple());
            assertEquals(2, tupleInput.getComponents().size());
        });
    }

    @Test
    void testExtractEventParametersWithTupleSupport_UsingReflection() throws Exception {
        // Create a simple Web3j Event
        List<TypeReference<?>> parameters = Arrays.asList(
            new TypeReference<Uint256>(true) {},
            new TypeReference<DynamicBytes>(false) {}
        );
        Event abiEvent = new Event("TestEvent", parameters);

        // Create ContractAbiEvent with tuple components
        var tupleComponents = Arrays.asList(
            new AbiParser.AbiEventInput("field1", "uint256", false),
            new AbiParser.AbiEventInput("field2", "string", false)
        );

        var eventInputs = Arrays.asList(
            new AbiParser.AbiEventInput("regularParam", "uint256", true),
            new AbiParser.AbiEventInput("tupleParam", "tuple", false, tupleComponents)
        );

        var contractAbiEvent = new AbiParser.ContractAbiEvent(abiEvent, eventInputs);

        // Create a mock log
        Log ethLog = new Log();
        ethLog.setAddress("******************************************");
        ethLog.setTopics(Arrays.asList(
            "0xeventSignature",
            "0x0000000000000000000000000000000000000000000000000000000000000001"
        ));
        ethLog.setData("0x0000000000000000000000000000000000000000000000000000000000000020");

        // Use reflection to call the private method
        Method extractMethod = EthEventLogDao.class.getDeclaredMethod(
            "extractEventParametersWithTupleSupport",
            org.web3j.abi.datatypes.Event.class,
            Log.class,
            AbiParser.ContractAbiEvent.class
        );
        extractMethod.setAccessible(true);

        // Call the method
        Object result = extractMethod.invoke(ethEventLogDao, abiEvent, ethLog, contractAbiEvent);

        // Verify that the method was called successfully
        // The result might be null due to mock data, but we can verify the method exists and runs
        System.out.println("✅ extractEventParametersWithTupleSupport method called successfully!");
        System.out.println("Result: " + result);

        // The method should detect tuple types and log appropriate messages
        // Since we're using mock data, the actual extraction might fail, but the tuple detection should work
        assertTrue(contractAbiEvent.getInputs().stream().anyMatch(input -> input.isTuple()),
                   "ContractAbiEvent should contain tuple types");
    }
}
